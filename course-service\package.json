{"name": "time-course-course-service", "version": "1.0.0", "description": "Course and Learning Management Service", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "nodemon src/server.ts", "build": "tsc", "test": "jest"}, "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "axios": "^1.5.0", "multer": "^1.4.5-lts.1", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "joi": "^17.9.2", "winston": "^3.10.0", "jsonwebtoken": "^9.0.2", "form-data": "^4.0.0", "sharp": "^0.32.5"}, "devDependencies": {"@types/express": "^4.17.17", "@types/node": "^20.5.0", "@types/multer": "^1.4.7", "@types/cors": "^2.8.13", "@types/jsonwebtoken": "^9.0.2", "typescript": "^5.1.6", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "jest": "^29.6.2", "@types/jest": "^29.5.3"}}