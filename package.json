{"name": "time-course-platform", "version": "1.0.0", "description": "English Online Course Platform with Microservices Architecture", "scripts": {"dev": "docker-compose up --build", "dev:detached": "docker-compose up -d --build", "dev:fast": "./scripts/dev-setup.sh start", "dev:db-only": "./scripts/dev-setup.sh db-only", "dev:stop": "./scripts/dev-setup.sh stop", "dev:status": "./scripts/dev-setup.sh status", "dev:restart": "./scripts/dev-setup.sh restart", "down": "docker-compose down", "logs": "docker-compose logs -f", "clean": "docker-compose down -v --remove-orphans", "seed-test": "node scripts/seed-test-data.js", "gen-tokens": "node scripts/generate-test-tokens.js", "db:list": "node scripts/db-manager.js list", "db:users": "node scripts/db-manager.js users", "db:clear-test": "node scripts/db-manager.js clear-test", "db:stats": "node scripts/db-manager.js stats"}, "keywords": ["microservices", "education", "english", "course"], "author": "Time Course Team", "license": "MIT", "devDependencies": {"mongoose": "^7.5.0", "jsonwebtoken": "^9.0.2"}}