{"name": "time-course-auth-service", "version": "1.0.0", "description": "Authentication and User Management Service", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "nodemon src/server.ts", "build": "tsc", "test": "jest"}, "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "firebase-admin": "^11.10.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "joi": "^17.9.2", "winston": "^3.10.0"}, "devDependencies": {"@types/express": "^4.17.17", "@types/node": "^20.5.0", "@types/jsonwebtoken": "^9.0.2", "@types/bcryptjs": "^2.4.2", "@types/cors": "^2.8.13", "typescript": "^5.1.6", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "jest": "^29.6.2", "@types/jest": "^29.5.3"}}