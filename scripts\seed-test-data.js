#!/usr/bin/env node

/**
 * Script untuk membuat test data dengan berbagai role user
 * Jalankan dengan: node scripts/seed-test-data.js
 */

const mongoose = require('mongoose');
const { UserRole, UserStatus } = require('../shared/types');

// MongoDB connection URIs
const AUTH_DB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/time_course_auth';

// User schema (simplified for seeding)
const userSchema = new mongoose.Schema({
  uid: { type: String, required: true, unique: true },
  email: { type: String, required: true, unique: true },
  displayName: { type: String, required: true },
  photoURL: { type: String, default: null },
  role: { type: String, enum: Object.values(UserRole), default: UserRole.STUDENT },
  status: { type: String, enum: Object.values(UserStatus), default: UserStatus.ACTIVE },
}, { timestamps: true });

const UserModel = mongoose.model('User', userSchema);

// Test users data
const testUsers = [
  {
    uid: 'test-admin-001',
    email: '<EMAIL>',
    displayName: 'Test Admin',
    role: UserRole.ADMIN,
    status: UserStatus.ACTIVE
  },
  {
    uid: 'test-tutor-001',
    email: '<EMAIL>',
    displayName: 'Test Tutor 1',
    role: UserRole.TUTOR,
    status: UserStatus.ACTIVE
  },
  {
    uid: 'test-tutor-002',
    email: '<EMAIL>',
    displayName: 'Test Tutor 2',
    role: UserRole.TUTOR,
    status: UserStatus.ACTIVE
  },
  {
    uid: 'test-student-001',
    email: '<EMAIL>',
    displayName: 'Test Student 1',
    role: UserRole.STUDENT,
    status: UserStatus.ACTIVE
  },
  {
    uid: 'test-student-002',
    email: '<EMAIL>',
    displayName: 'Test Student 2',
    role: UserRole.STUDENT,
    status: UserStatus.ACTIVE
  },
  {
    uid: 'test-student-003',
    email: '<EMAIL>',
    displayName: 'Test Student 3',
    role: UserRole.STUDENT,
    status: UserStatus.SUSPENDED
  }
];

async function seedTestData() {
  try {
    console.log('🌱 Starting test data seeding...');
    
    // Connect to auth database
    await mongoose.connect(AUTH_DB_URI);
    console.log('✅ Connected to auth database');

    // Clear existing test users
    await UserModel.deleteMany({ email: { $regex: '@test\.com$' } });
    console.log('🧹 Cleared existing test users');

    // Insert test users
    const createdUsers = await UserModel.insertMany(testUsers);
    console.log(`✅ Created ${createdUsers.length} test users`);

    // Display created users
    console.log('\n📋 Test Users Created:');
    console.log('='.repeat(80));
    
    createdUsers.forEach(user => {
      console.log(`👤 ${user.role.toUpperCase().padEnd(8)} | ${user.email.padEnd(25)} | ${user.displayName}`);
    });

    console.log('\n🔑 JWT Tokens for Testing:');
    console.log('='.repeat(80));
    console.log('Use these user IDs to generate JWT tokens for testing:');
    
    createdUsers.forEach(user => {
      console.log(`${user.role.toUpperCase().padEnd(8)} | UID: ${user.uid} | ID: ${user._id}`);
    });

    console.log('\n🚀 Test data seeding completed successfully!');
    
  } catch (error) {
    console.error('❌ Error seeding test data:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
  }
}

// Run the seeding
if (require.main === module) {
  seedTestData();
}

module.exports = { seedTestData, testUsers };
